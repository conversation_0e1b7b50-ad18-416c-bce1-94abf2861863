# Firebase Configuration for Tap2Go
# These environment variables are used by src/lib/firebase.ts

NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_firebase_app_id

# Firebase Admin SDK Configuration (Only 3 variables needed!)
FIREBASE_ADMIN_PROJECT_ID=your_project_id
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key_here\n-----END PRIVATE KEY-----\n"
FIREBASE_ADMIN_CLIENT_EMAIL=firebase-adminsdk-xxxxx@your_project_id.iam.gserviceaccount.com

# Google Maps API Keys (Separate for Security & Cost Control)
NEXT_PUBLIC_MAPS_FRONTEND_KEY=your_frontend_maps_api_key
MAPS_BACKEND_KEY=your_backend_maps_api_key

# Bonsai Elasticsearch Configuration (Free Sandbox Plan)
BONSAI_HOST=https://your_bonsai_host.bonsaisearch.net:443
BONSAI_USERNAME=your_bonsai_username
BONSAI_PASSWORD=your_bonsai_password

# Cloudinary Configuration for Tap2Go
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret
CLOUDINARY_WEBHOOK_SECRET=your_webhook_secret_here

# PayMongo Payment Gateway Configuration
PAYMONGO_PUBLIC_KEY_LIVE=pk_live_your_live_public_key
PAYMONGO_SECRET_KEY_LIVE=sk_live_your_live_secret_key
NEXT_PUBLIC_PAYMONGO_PUBLIC_KEY_LIVE=pk_live_your_live_public_key

# Firebase Cloud Messaging Configuration
NEXT_PUBLIC_FIREBASE_VAPID_KEY=your_vapid_key_here

# ========================================
# HYBRID DATABASE ARCHITECTURESUPABASE CONFIGURATION
# ========================================

# Supabase Project Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your_project_id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# CMS Configuration
ENABLE_SUPABASE_CMS=true

# ========================================
# UPSTASH REDIS CONFIGURATION (Enterprise Caching)
# ========================================

# Upstash Redis Credentials
UPSTASH_REDIS_REST_URL="https://your_redis_host.upstash.io"
UPSTASH_REDIS_REST_TOKEN="your_upstash_redis_token"

# Cache Configuration
ENABLE_REDIS_CACHING=true
REDIS_DEFAULT_TTL=3600

# ========================================
# RESEND EMAIL SERVICE CONFIGURATION
# ========================================

# Resend API Configuration
RESEND_API_KEY=re_your_resend_api_key
NEXT_PUBLIC_RESEND_FROM_EMAIL=<EMAIL>

# Email Configuration
ENABLE_EMAIL_NOTIFICATIONS=true
EMAIL_FROM_NAME=Tap2Go
EMAIL_REPLY_TO=<EMAIL>

# ========================================
# GOOGLE AI STUDIO (GEMINI) CONFIGURATION
# ========================================

# Google AI Studio API Key
GOOGLE_AI_API_KEY=your_google_ai_api_key

# AI Configuration
ENABLE_AI_FEATURES=true
AI_MODEL_DEFAULT=gemini-1.5-flash

# ========================================
# EXPO CLI CONFIGURATION (Mobile Development)
# ========================================

# ENTERPRISE CONFIGURATION - ENABLE CACHING FOR PERFORMANCE
# Caching is REQUIRED for enterprise development and certificate management
# EXPO_NO_CACHE=1  # ❌ DISABLED - This was causing networking and certificate issues
EXPO_BETA=false
EXPO_USE_FAST_RESOLVER=true

# Enable development certificate caching for offline development
EXPO_CACHE_CERTIFICATES=true
