{"name": "shared-types", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "npx tsc", "dev": "npx tsc --watch", "lint": "eslint src/", "type-check": "npx tsc --noEmit", "clean": "rm -rf dist"}, "peerDependencies": {"react": "^19.0.0", "@types/react": "^19.1.0"}, "devDependencies": {"react": "^19.0.0", "@types/react": "^19.1.0", "typescript": "^5.8.3"}}